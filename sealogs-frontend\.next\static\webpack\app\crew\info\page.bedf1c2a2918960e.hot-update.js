"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/voyages.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/crew/voyages.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/plugin/isBetween */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isBetween.js\");\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Extend dayjs with isBetween plugin\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default()));\nconst CrewVoyages = (param)=>{\n    let { voyages } = param;\n    _s();\n    // State management for filters using nuqs\n    const [dateRangeFilter, setDateRangeFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState)(\"voyageDateRange\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [vesselFilter, setVesselFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState)(\"voyageVessel\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [dutyFilter, setDutyFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState)(\"voyageDuty\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    // Local state for filter values\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        dateRange: null,\n        vessel: null,\n        duty: null\n    });\n    const formatDateWithTime = (dateTime)=>{\n        if (dateTime) {\n            const [date, time] = dateTime.split(\" \");\n            const [year, month, day] = date.split(\"-\");\n            return \"\".concat(day, \"/\").concat(month, \"/\").concat(year.slice(-2), \" at \").concat(time);\n        }\n    };\n    // Filter voyages based on active filters\n    const filteredVoyages = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        let filtered = [\n            ...voyages\n        ];\n        // Apply date range filter\n        if (filters.dateRange && (filters.dateRange.startDate || filters.dateRange.endDate)) {\n            filtered = filtered.filter((voyage)=>{\n                const voyageDate = voyage.punchIn ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.punchIn) : dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.logBookEntry.startDate);\n                if (filters.dateRange.startDate && filters.dateRange.endDate) {\n                    return voyageDate.isBetween(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.startDate).startOf(\"day\"), dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.endDate).endOf(\"day\"), null, \"[]\");\n                } else if (filters.dateRange.startDate) {\n                    return voyageDate.isAfter(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.startDate).startOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.startDate), \"day\");\n                } else if (filters.dateRange.endDate) {\n                    return voyageDate.isBefore(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.endDate).endOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.endDate), \"day\");\n                }\n                return true;\n            });\n        }\n        // Apply vessel filter\n        if (filters.vessel) {\n            const vesselId = String(filters.vessel.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_logBookEntry_vehicle, _voyage_logBookEntry;\n                const voyageVesselId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : (_voyage_logBookEntry_vehicle = _voyage_logBookEntry.vehicle) === null || _voyage_logBookEntry_vehicle === void 0 ? void 0 : _voyage_logBookEntry_vehicle.id);\n                return voyageVesselId === vesselId;\n            });\n        }\n        // Apply duty performed filter\n        if (filters.duty) {\n            const dutyId = String(filters.duty.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_dutyPerformed;\n                const voyageDutyId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_dutyPerformed = voyage.dutyPerformed) === null || _voyage_dutyPerformed === void 0 ? void 0 : _voyage_dutyPerformed.id);\n                return voyageDutyId === dutyId;\n            });\n        }\n        return filtered;\n    }, [\n        voyages,\n        filters\n    ]);\n    // Calculate sea time in hours\n    const calculateSeaTime = (punchIn, punchOut)=>{\n        if (!punchIn || !punchOut) return \"0\";\n        const hours = Math.floor((dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchOut).valueOf() - dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchIn).valueOf()) / (1000 * 60 * 60));\n        return isNaN(hours) ? \"0\" : hours.toString();\n    };\n    // Handle filter changes from main Filter component\n    const handleFilterChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n        // Sync with URL parameters\n        if (type === \"dateRange\") {\n            setDateRangeFilter(data ? JSON.stringify(data) : \"\");\n        } else if (type === \"vessel\") {\n            setVesselFilter(data ? JSON.stringify(data) : \"\");\n        } else if (type === \"duty\") {\n            setDutyFilter(data ? JSON.stringify(data) : \"\");\n        }\n    };\n    // Initialize filters from URL on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        try {\n            if (dateRangeFilter) {\n                const parsed = JSON.parse(dateRangeFilter);\n                setFilters((prev)=>({\n                        ...prev,\n                        dateRange: parsed\n                    }));\n            }\n            if (vesselFilter) {\n                const parsed = JSON.parse(vesselFilter);\n                setFilters((prev)=>({\n                        ...prev,\n                        vessel: parsed\n                    }));\n            }\n            if (dutyFilter) {\n                const parsed = JSON.parse(dutyFilter);\n                setFilters((prev)=>({\n                        ...prev,\n                        duty: parsed\n                    }));\n            }\n        } catch (error) {\n            console.warn(\"Error parsing filter values from URL:\", error);\n        }\n    }, [\n        dateRangeFilter,\n        vesselFilter,\n        dutyFilter\n    ]);\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"date\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.punchIn ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.punchIn) : (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.logBookEntry.startDate);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original_logBookEntry, _rowA_original1, _rowB_original, _rowB_original_logBookEntry, _rowB_original1;\n                const dateA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.punchIn) ? new Date(rowA.original.punchIn).getTime() : new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_logBookEntry = _rowA_original1.logBookEntry) === null || _rowA_original_logBookEntry === void 0 ? void 0 : _rowA_original_logBookEntry.startDate) || 0).getTime();\n                const dateB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.punchIn) ? new Date(rowB.original.punchIn).getTime() : new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_logBookEntry = _rowB_original1.logBookEntry) === null || _rowB_original_logBookEntry === void 0 ? void 0 : _rowB_original_logBookEntry.startDate) || 0).getTime();\n                return dateB - dateA // Most recent first\n                ;\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.logBookEntry.vehicle.title;\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_logBookEntry_vehicle, _rowA_original_logBookEntry, _rowA_original, _rowB_original_logBookEntry_vehicle, _rowB_original_logBookEntry, _rowB_original;\n                const vesselA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_logBookEntry = _rowA_original.logBookEntry) === null || _rowA_original_logBookEntry === void 0 ? void 0 : (_rowA_original_logBookEntry_vehicle = _rowA_original_logBookEntry.vehicle) === null || _rowA_original_logBookEntry_vehicle === void 0 ? void 0 : _rowA_original_logBookEntry_vehicle.title) || \"\";\n                const vesselB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_logBookEntry = _rowB_original.logBookEntry) === null || _rowB_original_logBookEntry === void 0 ? void 0 : (_rowB_original_logBookEntry_vehicle = _rowB_original_logBookEntry.vehicle) === null || _rowB_original_logBookEntry_vehicle === void 0 ? void 0 : _rowB_original_logBookEntry_vehicle.title) || \"\";\n                return vesselA.localeCompare(vesselB);\n            }\n        },\n        {\n            accessorKey: \"dutyPerformed\",\n            header: \"Duty performed\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.dutyPerformed.title;\n            }\n        },\n        {\n            accessorKey: \"signIn\",\n            header: \"Sign in\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchIn);\n            }\n        },\n        {\n            accessorKey: \"signOut\",\n            header: \"Sign out\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchOut);\n            }\n        },\n        {\n            accessorKey: \"totalSeaTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Total sea time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut);\n                return \"\".concat(hours, \" Hours\");\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const hoursA = parseInt(calculateSeaTime(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.punchIn, rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.punchOut)) || 0;\n                const hoursB = parseInt(calculateSeaTime(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.punchIn, rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.punchOut)) || 0;\n                return hoursB - hoursA // Highest hours first\n                ;\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: !voyages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_1__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 276,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n            columns: columns,\n            data: filteredVoyages,\n            showToolbar: false,\n            pageSize: 20\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 278,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n        lineNumber: 274,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewVoyages, \"p43lIqQFMdij5OTtnEiMm2GK2kk=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState\n    ];\n});\n_c = CrewVoyages;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewVoyages);\nvar _c;\n$RefreshReg$(_c, \"CrewVoyages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/voyages.tsx\n"));

/***/ })

});