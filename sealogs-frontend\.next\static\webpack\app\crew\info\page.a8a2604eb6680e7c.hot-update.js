"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/voyages.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/crew/voyages.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/plugin/isBetween */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isBetween.js\");\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Extend dayjs with isBetween plugin\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default()));\nconst CrewVoyages = (param)=>{\n    let { voyages } = param;\n    _s();\n    // State management for filters using nuqs\n    const [dateRangeFilter, setDateRangeFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState)(\"voyageDateRange\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [vesselFilter, setVesselFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState)(\"voyageVessel\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [dutyFilter, setDutyFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState)(\"voyageDuty\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    // Local state for filter values\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        dateRange: null,\n        vessel: null,\n        duty: null\n    });\n    const formatDateWithTime = (dateTime)=>{\n        if (dateTime) {\n            const [date, time] = dateTime.split(\" \");\n            const [year, month, day] = date.split(\"-\");\n            return \"\".concat(day, \"/\").concat(month, \"/\").concat(year.slice(-2), \" at \").concat(time);\n        }\n    };\n    // Filter voyages based on active filters\n    const filteredVoyages = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        let filtered = [\n            ...voyages\n        ];\n        // Apply date range filter\n        if (filters.dateRange && (filters.dateRange.startDate || filters.dateRange.endDate)) {\n            filtered = filtered.filter((voyage)=>{\n                const voyageDate = voyage.punchIn ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.punchIn) : dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.logBookEntry.startDate);\n                if (filters.dateRange.startDate && filters.dateRange.endDate) {\n                    return voyageDate.isBetween(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.startDate).startOf(\"day\"), dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.endDate).endOf(\"day\"), null, \"[]\");\n                } else if (filters.dateRange.startDate) {\n                    return voyageDate.isAfter(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.startDate).startOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.startDate), \"day\");\n                } else if (filters.dateRange.endDate) {\n                    return voyageDate.isBefore(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.endDate).endOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.endDate), \"day\");\n                }\n                return true;\n            });\n        }\n        // Apply vessel filter\n        if (filters.vessel) {\n            const vesselId = String(filters.vessel.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_logBookEntry_vehicle, _voyage_logBookEntry;\n                const voyageVesselId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : (_voyage_logBookEntry_vehicle = _voyage_logBookEntry.vehicle) === null || _voyage_logBookEntry_vehicle === void 0 ? void 0 : _voyage_logBookEntry_vehicle.id);\n                return voyageVesselId === vesselId;\n            });\n        }\n        // Apply duty performed filter\n        if (filters.duty) {\n            const dutyId = String(filters.duty.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_dutyPerformed;\n                const voyageDutyId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_dutyPerformed = voyage.dutyPerformed) === null || _voyage_dutyPerformed === void 0 ? void 0 : _voyage_dutyPerformed.id);\n                return voyageDutyId === dutyId;\n            });\n        }\n        return filtered;\n    }, [\n        voyages,\n        filters\n    ]);\n    // Calculate sea time in hours\n    const calculateSeaTime = (punchIn, punchOut)=>{\n        if (!punchIn || !punchOut) return \"0\";\n        const hours = Math.floor((dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchOut).valueOf() - dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchIn).valueOf()) / (1000 * 60 * 60));\n        return isNaN(hours) ? \"0\" : hours.toString();\n    };\n    // Handle filter changes from main Filter component\n    const handleFilterChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n        // Sync with URL parameters\n        if (type === \"dateRange\") {\n            setDateRangeFilter(data ? JSON.stringify(data) : \"\");\n        } else if (type === \"vessel\") {\n            setVesselFilter(data ? JSON.stringify(data) : \"\");\n        } else if (type === \"duty\") {\n            setDutyFilter(data ? JSON.stringify(data) : \"\");\n        }\n    };\n    // Initialize filters from URL on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        try {\n            if (dateRangeFilter) {\n                const parsed = JSON.parse(dateRangeFilter);\n                setFilters((prev)=>({\n                        ...prev,\n                        dateRange: parsed\n                    }));\n            }\n            if (vesselFilter) {\n                const parsed = JSON.parse(vesselFilter);\n                setFilters((prev)=>({\n                        ...prev,\n                        vessel: parsed\n                    }));\n            }\n            if (dutyFilter) {\n                const parsed = JSON.parse(dutyFilter);\n                setFilters((prev)=>({\n                        ...prev,\n                        duty: parsed\n                    }));\n            }\n        } catch (error) {\n            console.warn(\"Error parsing filter values from URL:\", error);\n        }\n    }, [\n        dateRangeFilter,\n        vesselFilter,\n        dutyFilter\n    ]);\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"date\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.punchIn ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.punchIn) : (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.logBookEntry.startDate);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original_logBookEntry, _rowA_original1, _rowB_original, _rowB_original_logBookEntry, _rowB_original1;\n                const dateA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.punchIn) ? new Date(rowA.original.punchIn).getTime() : new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_logBookEntry = _rowA_original1.logBookEntry) === null || _rowA_original_logBookEntry === void 0 ? void 0 : _rowA_original_logBookEntry.startDate) || 0).getTime();\n                const dateB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.punchIn) ? new Date(rowB.original.punchIn).getTime() : new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_logBookEntry = _rowB_original1.logBookEntry) === null || _rowB_original_logBookEntry === void 0 ? void 0 : _rowB_original_logBookEntry.startDate) || 0).getTime();\n                return dateB - dateA // Most recent first\n                ;\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.logBookEntry.vehicle.title;\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_logBookEntry_vehicle, _rowA_original_logBookEntry, _rowA_original, _rowB_original_logBookEntry_vehicle, _rowB_original_logBookEntry, _rowB_original;\n                const vesselA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_logBookEntry = _rowA_original.logBookEntry) === null || _rowA_original_logBookEntry === void 0 ? void 0 : (_rowA_original_logBookEntry_vehicle = _rowA_original_logBookEntry.vehicle) === null || _rowA_original_logBookEntry_vehicle === void 0 ? void 0 : _rowA_original_logBookEntry_vehicle.title) || \"\";\n                const vesselB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_logBookEntry = _rowB_original.logBookEntry) === null || _rowB_original_logBookEntry === void 0 ? void 0 : (_rowB_original_logBookEntry_vehicle = _rowB_original_logBookEntry.vehicle) === null || _rowB_original_logBookEntry_vehicle === void 0 ? void 0 : _rowB_original_logBookEntry_vehicle.title) || \"\";\n                return vesselA.localeCompare(vesselB);\n            }\n        },\n        {\n            accessorKey: \"dutyPerformed\",\n            header: \"Duty performed\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.dutyPerformed.title;\n            }\n        },\n        {\n            accessorKey: \"signIn\",\n            header: \"Sign in\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchIn);\n            }\n        },\n        {\n            accessorKey: \"signOut\",\n            header: \"Sign out\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchOut);\n            }\n        },\n        {\n            accessorKey: \"totalSeaTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Total sea time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut);\n                return \"\".concat(hours, \" Hours\");\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const hoursA = parseInt(calculateSeaTime(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.punchIn, rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.punchOut)) || 0;\n                const hoursB = parseInt(calculateSeaTime(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.punchIn, rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.punchOut)) || 0;\n                return hoursB - hoursA // Highest hours first\n                ;\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: !voyages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_1__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 278,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n            columns: columns,\n            data: filteredVoyages,\n            showToolbar: false,\n            pageSize: 20\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 280,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n        lineNumber: 276,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewVoyages, \"p43lIqQFMdij5OTtnEiMm2GK2kk=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_8__.useQueryState\n    ];\n});\n_c = CrewVoyages;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewVoyages);\nvar _c;\n$RefreshReg$(_c, \"CrewVoyages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/voyages.tsx\n"));

/***/ })

});