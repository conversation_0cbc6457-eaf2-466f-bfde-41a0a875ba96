"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/view.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/view.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CrewView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _crew_training_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../crew-training/list */ \"(app-pages-browser)/./src/app/ui/crew-training/list.tsx\");\n/* harmony import */ var _crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../crew/allocated-tasks */ \"(app-pages-browser)/./src/app/ui/crew/allocated-tasks.tsx\");\n/* harmony import */ var _crew_voyages__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../crew/voyages */ \"(app-pages-browser)/./src/app/ui/crew/voyages.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/maintenanceHelper */ \"(app-pages-browser)/./src/app/helpers/maintenanceHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* ---------- shadcn/ui replacements ------------------------------------ */ \n\n\n/* ---------------------------------------------------------------------- */ function CrewView(param) {\n    let { crewId } = param;\n    var _crewInfo_status, _crewInfo_status1, _crewInfo_vehicles, _crewInfo_departments;\n    _s();\n    /* ---------------- state / helpers ----------------------------------- */ const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"tab\", {\n        defaultValue: \"training\",\n        serialize: (value)=>value,\n        parse: (value)=>{\n            if ([\n                \"training\",\n                \"qualification\",\n                \"allocatedTasks\",\n                \"voyages\"\n            ].includes(value)) {\n                return value;\n            }\n            return \"training\";\n        }\n    });\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dueTrainingCounter, setDueTrainingCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [taskList, setTaskList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [voyages, setVoyages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSelf, setIsSelf] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [archiveOpen, setArchiveOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /* ---------------- data fetch ---------------------------------------- */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewMembersLogBookEntrySections)(crewId, setVoyages);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.getPermissions);\n    }, []);\n    /* vessels ------------------------------------------------------------ */ const handleSetVessels = (vsls)=>{\n        const activeVessels = vsls.filter((v)=>!v.archived);\n        setVessels(activeVessels.map((v)=>({\n                label: v.title,\n                value: v.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getVesselList)(handleSetVessels);\n    /* tasks -------------------------------------------------------------- */ const handleSetTaskList = (tasks)=>{\n        const active = tasks.filter((t)=>!t.archived).map((t)=>({\n                ...t,\n                isOverDue: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.isOverDueTask)(t)\n            }));\n        const list = (0,_app_helpers_maintenanceHelper__WEBPACK_IMPORTED_MODULE_10__.sortMaintenanceChecks)(active.map((mc)=>({\n                id: mc.id,\n                name: mc.name,\n                basicComponentID: mc.basicComponentID,\n                comments: mc.comments,\n                description: mc.description,\n                assignedToID: mc.assignedToID,\n                expires: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.upcomingScheduleDate)(mc),\n                status: mc.status,\n                startDate: mc.startDate,\n                isOverDue: mc.isOverDue,\n                basicComponent: mc.basicComponent,\n                isCompleted: mc.status === \"Completed\" ? \"1\" : \"2\"\n            })));\n        setTaskList(list);\n        setTaskCounter(active.filter((t)=>![\n                \"Completed\",\n                \"Save_As_Draft\"\n            ].includes(t.status) && ![\n                \"Completed\",\n                \"Upcoming\"\n            ].includes(t.isOverDue.status)).length);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getComponentMaintenanceCheckByMemberId)(crewId, handleSetTaskList);\n    /* crew info ---------------------------------------------------------- */ const handleSetCrewInfo = (info)=>{\n        var _withTraining_trainingSessionsDue;\n        setCrewInfo(info);\n        const [withTraining] = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            info\n        ], vessels);\n        var _withTraining_trainingSessionsDue_nodes_filter;\n        const dues = (_withTraining_trainingSessionsDue_nodes_filter = withTraining === null || withTraining === void 0 ? void 0 : (_withTraining_trainingSessionsDue = withTraining.trainingSessionsDue) === null || _withTraining_trainingSessionsDue === void 0 ? void 0 : _withTraining_trainingSessionsDue.nodes.filter((n)=>n.status.isOverdue || n.status.dueWithinSevenDays)) !== null && _withTraining_trainingSessionsDue_nodes_filter !== void 0 ? _withTraining_trainingSessionsDue_nodes_filter : [];\n        setDueTrainingCounter(dues.length);\n        if (localStorage.getItem(\"userId\") === info.id) setIsSelf(true);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.getCrewByID)(crewId, handleSetCrewInfo);\n    /* archive / retrieve user ------------------------------------------- */ const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>router.back(),\n        onError: (err)=>console.error(\"mutationUpdateUser error\", err)\n    });\n    const handleArchiveUser = async (info)=>{\n        if (!(info && info.id > 0)) return;\n        await mutationUpdateUser({\n            variables: {\n                input: {\n                    id: info.id,\n                    isArchived: !info.isArchived\n                }\n            }\n        });\n    };\n    /* permission helpers ------------------------------------------------- */ const noPerm = (perm)=>!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(perm, permissions);\n    const BadgeCounter = (param)=>{\n        let { count } = param;\n        return count ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"ml-2 flex h-5 w-5 items-center justify-center rounded-full border border-rose-600 bg-rose-100 text-xs font-medium text-rose-600\",\n            children: count\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 185,\n            columnNumber: 13\n        }, this) : null;\n    };\n    /* early exit if no access ------------------------------------------- */ if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_MEMBER_CONTACT\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 197,\n            columnNumber: 13\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops! You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n            lineNumber: 199,\n            columnNumber: 13\n        }, this);\n    }\n    /* active log-book ---------------------------------------------------- */ const activeLog = voyages && voyages.length > 0 && !voyages[0].punchOut ? voyages[0] : null;\n    /* ----------------------- render ------------------------------------ */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col justify-between md:flex-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.H2, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2 font-medium\",\n                                children: \"Crew:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1\",\n                                children: !crewInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_12__.Skeleton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 29\n                                }, this) : \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: (crewInfo === null || crewInfo === void 0 ? void 0 : (_crewInfo_status = crewInfo.status) === null || _crewInfo_status === void 0 ? void 0 : _crewInfo_status.state) === \"Active\" ? \"primary\" : \"warning\",\n                                className: \"hidden min-w-fit rounded h-fit py-0.5 px-1.5 text-sm font-normal lg:inline ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                variant: (crewInfo === null || crewInfo === void 0 ? void 0 : (_crewInfo_status1 = crewInfo.status) === null || _crewInfo_status1 === void 0 ? void 0 : _crewInfo_status1.state) === \"Active\" ? \"primary\" : \"warning\",\n                                className: \"block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2\",\n                                children: crewInfo.isArchived ? \"Archived\" : activeLog ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/log-entries/view?&vesselID=\".concat(activeLog.logBookEntry.vehicle.id, \"&logentryID=\").concat(activeLog.logBookEntry.id),\n                                    children: [\n                                        \"Active log book at\",\n                                        \" \",\n                                        activeLog.logBookEntry.vehicle.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 29\n                                }, this) : \"No active log books\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center justify-end gap-2\",\n                        children: [\n                            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                                open: archiveOpen,\n                                onOpenChange: setArchiveOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                        className: \"sm:max-w-md\",\n                                        children: (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.DELETE_MEMBER || \"DELETE_MEMBER\", permissions) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: [\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\",\n                                                                \" \",\n                                                                \"User\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogDescription, {\n                                                            children: [\n                                                                \"Are you sure you want to\",\n                                                                \" \",\n                                                                (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"retrieve\" : \"archive\",\n                                                                \" \",\n                                                                \"\".concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.firstName) || \"this user\", \" \").concat((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.surname) || \"\"),\n                                                                \" \",\n                                                                \"?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end gap-2 pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            variant: \"outline\",\n                                                            onClick: ()=>setArchiveOpen(false),\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                            onClick: ()=>{\n                                                                handleArchiveUser(crewInfo);\n                                                                setArchiveOpen(false);\n                                                            },\n                                                            children: (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.isArchived) ? \"Retrieve\" : \"Archive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                                        children: \"Warning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-3 text-slate-500\",\n                                                    children: \"You do not have permission to archive user.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_14__.DialogFooter, {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setArchiveOpen(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 29\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/edit?id=\".concat(crewId)),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 25\n                            }, this),\n                            (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.EDIT_MEMBER || \"EDIT_MEMBER\", permissions) || isSelf) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/user/create\"),\n                                className: \"\".concat(tab === \"training\" ? \"hidden\" : \"\", \" \").concat(tab === \"qualification\" ? \"!mr-0\" : \"\"),\n                                children: \"Add Qualification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 25\n                            }, this),\n                            permissions && tab !== \"qualification\" && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                onClick: ()=>router.push(\"/crew-training/create?memberId=\".concat(crewId)),\n                                children: \"Record Training\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 213,\n                columnNumber: 13\n            }, this),\n            ((crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.email) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.vehicles) || (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.phoneNumber)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4\",\n                children: [\n                    (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo.primaryDuty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Primary Duty:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ms-2\",\n                                children: crewInfo.primaryDuty.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 25\n                    }, this),\n                    [\n                        \"email\",\n                        \"phoneNumber\"\n                    ].map((field)=>(permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(process.env.VIEW_MEMBER_CONTACT || \"VIEW_MEMBER_CONTACT\", permissions) || isSelf) && (crewInfo === null || crewInfo === void 0 ? void 0 : crewInfo[field]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"mr-4 w-32\",\n                                    children: field === \"email\" ? \"Email:\" : \"Phone:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ms-2\",\n                                    children: crewInfo[field]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, field, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 33\n                        }, this)),\n                    ((_crewInfo_vehicles = crewInfo.vehicles) === null || _crewInfo_vehicles === void 0 ? void 0 : _crewInfo_vehicles.nodes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Vessels:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.vehicles.nodes.map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(v.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: v.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, v.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 25\n                    }, this),\n                    ((_crewInfo_departments = crewInfo.departments) === null || _crewInfo_departments === void 0 ? void 0 : _crewInfo_departments.nodes) && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2 mt-4 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-4 w-32\",\n                                children: \"Departments:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap md:flex-nowrap\",\n                                children: crewInfo.departments.nodes.map((d)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/department/info?id=\".concat(d.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ms-2 my-1 rounded border py-1 px-2 md:my-0\",\n                                            children: d.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, d.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 45\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 404,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.Tabs, {\n                    value: tab,\n                    onValueChange: (v)=>setTab(v),\n                    className: \"pt-2 pb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsList, {\n                            className: \"gap-2\",\n                            children: [\n                                (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_9__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"training\",\n                                    children: [\n                                        \"Training\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: dueTrainingCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.Tooltip, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                                    value: \"qualification\",\n                                                    disabled: true,\n                                                    children: \"Qualifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipContent, {\n                                            side: \"bottom\",\n                                            children: \"Coming soon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"allocatedTasks\",\n                                    children: [\n                                        \"Allocated Tasks\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BadgeCounter, {\n                                            count: taskCounter\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsTrigger, {\n                                    value: \"voyages\",\n                                    children: \"Voyages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"training\",\n                            children: noPerm(\"VIEW_MEMBER_TRAINING\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_training_list__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                memberId: crewId,\n                                excludeFilters: [\n                                    \"crew\",\n                                    \"overdueToggle\"\n                                ]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"qualification\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"allocatedTasks\",\n                            children: noPerm(\"VIEW_MEMBER_TASKS\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_allocated_tasks__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                taskList: taskList\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_16__.TabsContent, {\n                            value: \"voyages\",\n                            children: noPerm(\"VIEW_MEMBER_VOYAGES\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                errorMessage: \"Oops! You do not have permission to view this section.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_voyages__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                voyages: voyages\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n                lineNumber: 482,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\view.tsx\",\n        lineNumber: 211,\n        columnNumber: 9\n    }, this);\n}\n_s(CrewView, \"A7VUUPb+Tfb3R8d4PeyBGNikOac=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = CrewView;\nvar _c;\n$RefreshReg$(_c, \"CrewView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/view.tsx\n"));

/***/ })

});